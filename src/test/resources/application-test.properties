# Test profile configuration
spring.datasource.url=jdbc:h2:mem:testdb;MODE=LEGACY;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate properties for tests
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# H2 Console (for debugging if needed)
spring.h2.console.enabled=true

# Logging configuration for tests
logging.level.org.springframework.web=INFO
logging.level.org.example.back=DEBUG
logging.level.org.hibernate.SQL=INFO

# Disable banner for cleaner test output
spring.main.banner-mode=off
