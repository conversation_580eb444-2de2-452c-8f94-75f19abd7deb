package org.example.back.entity

import jakarta.persistence.*
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

@Entity
@Table(name = "users")
class User(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,

    @field:Email(message = "Email should be valid")
    @field:NotBlank(message = "Email is required")
    @Column(nullable = false, unique = true)
    var email: String = "",

    @field:NotBlank(message = "First name is required")
    @Column(nullable = false)
    var firstName: String = "",

    @field:NotBlank(message = "Last name is required")
    @Column(nullable = false)
    var lastName: String = "",

    @Column(nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // No-arg constructor for JPA
    constructor() : this(
        id = 0,
        email = "",
        firstName = "",
        lastName = "",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other) return true
        if (other !is User) return false
        return id == other.id
    }

    override fun hashCode(): Int = id.hashCode()

    override fun toString(): String = "User(id=$id, email='$email', firstName='$firstName', lastName='$lastName')"
}
