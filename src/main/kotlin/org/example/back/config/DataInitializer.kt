package org.example.back.config

import org.example.back.entity.User
import org.example.back.repository.UserRepository
import org.springframework.boot.CommandLineRunner
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class DataInitializer(private val userRepository: UserRepository) : CommandLineRunner {

    override fun run(vararg args: String?) {
        if (userRepository.count().toInt() == 0) {
            val users = listOf(
                User(
                    email = "<EMAIL>",
                    firstName = "John",
                    lastName = "Doe",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                ),
                User(
                    email = "<EMAIL>",
                    firstName = "Jane",
                    lastName = "Smith",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                ),
                User(
                    email = "<EMAIL>",
                    firstName = "Bob",
                    lastName = "<PERSON>",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                ),
                User(
                    email = "<EMAIL>",
                    firstName = "Bob",
                    lastName = "Johnson",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                )
            )

            userRepository.saveAll(users)
            println("Sample users created successfully")
        }
    }
}
