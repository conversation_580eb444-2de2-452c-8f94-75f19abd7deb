package org.example.back.controller

import org.example.back.service.UserService
import org.example.back.dto.BaseResponse
import org.example.back.entity.User
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = ["http://localhost:5173"])
class UserController(private val userService: UserService) {

    @GetMapping("/users")
    fun getUsers(): ResponseEntity<BaseResponse<List<User>>> {
        return formResponse(userService.getUsers(), mapOf())
    }

    @GetMapping("/users/me")
    fun getMe(): ResponseEntity<BaseResponse<User>> {
        return formResponse(userService.getUserByEmail("<EMAIL>")!!, mapOf("delete" to "/me/delete"))
    }

    @DeleteMapping("/users/me/delete")
    fun deleteMe(): ResponseEntity<BaseResponse<Unit>> {
        return formResponse(Unit, mapOf())
    }
    private fun<T> formResponse(response: T, links: Map<String, String>): ResponseEntity<BaseResponse<T>> {
        return ResponseEntity.ok(BaseResponse(response, links))
    }
}
