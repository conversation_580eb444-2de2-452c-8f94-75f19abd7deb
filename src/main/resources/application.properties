spring.application.name=back

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

frontend.url=http://localhost:5173

# Spring Security OAuth2 Client Configuration for Amazon
spring.security.oauth2.client.registration.amazon.client-id=amzn1.application-oa2-client.3d996e08dbdb4be2b72229b9be3cb478
spring.security.oauth2.client.registration.amazon.client-secret=amzn1.oa2-cs.v1.687c3741659d155dc5d4a9ebe61bfe82c4b157fb19d1e66d48fbbd7af2265dd1
spring.security.oauth2.client.registration.amazon.client-name=Login with Amazon
spring.security.oauth2.client.registration.amazon.scope=profile,profile:user_id
spring.security.oauth2.client.registration.amazon.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}
spring.security.oauth2.client.registration.amazon.client-authentication-method=client_secret_post
spring.security.oauth2.client.registration.amazon.authorization-grant-type=authorization_code

# Spring Security OAuth2 Provider Configuration for Amazon
spring.security.oauth2.client.provider.amazon.authorization-uri=https://www.amazon.com/ap/oa
spring.security.oauth2.client.provider.amazon.token-uri=https://api.amazon.com/auth/o2/token
spring.security.oauth2.client.provider.amazon.user-info-uri=https://api.amazon.com/user/profile
spring.security.oauth2.client.provider.amazon.user-name-attribute=user_id

# Server Configuration
server.port=8080
server.forward-headers-strategy=native

# Logging Configuration for Request/Response
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.apache.http=DEBUG
logging.level.httpclient.wire=DEBUG
logging.level.org.springframework.web.servlet.DispatcherServlet=DEBUG
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=DEBUG

# Enable request details logging
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=on-param
server.error.include-exception=false

# Spring Boot Actuator endpoints (optional - for monitoring)
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
