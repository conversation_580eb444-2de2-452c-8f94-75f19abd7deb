# User CRUD API with Spring Data REST

A Spring Boot REST API for managing users using Spring Data REST repositories with automatic endpoint generation.

## Features

- Automatic CRUD REST endpoints generated by Spring Data REST
- Input validation with proper error messages
- H2 in-memory database for development
- Repository event handlers for timestamp management
- Sample data initialization
- HATEOAS support (Hypermedia as the Engine of Application State)

## API Endpoints

### Base URL: `http://localhost:8080/api`

Spring Data REST automatically generates the following endpoints:

### 1. Get All Users
- **GET** `/api/users`
- **Response:** HAL+JSON format with embedded users and navigation links

### 2. Create User
- **POST** `/api/users`
- **Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```
- **Response:** `201 Created` with user data and links

### 3. Get User by ID
- **GET** `/api/users/{id}`
- **Response:** User data with navigation links

### 4. Update User (Full Update)
- **PUT** `/api/users/{id}`
- **Request Body:** Complete user object
```json
{
  "email": "<EMAIL>",
  "firstName": "Updated",
  "lastName": "Name"
}
```

### 5. Partial Update User
- **PATCH** `/api/users/{id}`
- **Request Body:** Only fields to update
```json
{
  "firstName": "NewName"
}
```

### 6. Delete User
- **DELETE** `/api/users/{id}`
- **Response:** `204 No Content`

### 7. Search by Email
- **GET** `/api/users/search/findByEmail?email=<EMAIL>`
- **Response:** User matching the email

### 8. API Profile and Metadata
- **GET** `/api/profile/users` - Get repository metadata
- **GET** `/api` - Get API root with available resources

## Spring Data REST Features

### HATEOAS Links
All responses include navigation links:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "createdAt": "2025-09-21T10:30:00",
  "updatedAt": "2025-09-21T10:30:00",
  "_links": {
    "self": {
      "href": "http://localhost:8080/api/users/1"
    },
    "user": {
      "href": "http://localhost:8080/api/users/1"
    },
    "users": {
      "href": "http://localhost:8080/api/users"
    }
  }
}
```

### Pagination and Sorting
- **GET** `/api/users?page=0&size=10&sort=lastName,asc`
- **GET** `/api/users?page=1&size=5&sort=createdAt,desc`

## Running the Application

1. Start the application:
```bash
./gradlew bootRun
```

2. Access H2 Console (for database inspection):
   - URL: http://localhost:8080/h2-console
   - JDBC URL: `jdbc:h2:mem:testdb`
   - Username: `sa`
   - Password: `password`

## Sample cURL Commands

```bash
# Get API root
curl -X GET http://localhost:8080/api

# Get all users (with pagination)
curl -X GET http://localhost:8080/api/users

# Create a new user
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","firstName":"Test","lastName":"User"}'

# Get user by ID
curl -X GET http://localhost:8080/api/users/1

# Full update user
curl -X PUT http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","firstName":"Updated","lastName":"Name"}'

# Partial update user
curl -X PATCH http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"firstName":"NewFirstName"}'

# Search by email
curl -X GET "http://localhost:8080/api/users/search/findByEmail?email=<EMAIL>"

# Delete user
curl -X DELETE http://localhost:8080/api/users/1

# Get users with pagination and sorting
curl -X GET "http://localhost:8080/api/users?page=0&size=2&sort=lastName,asc"
```

## Key Differences from Manual Controllers

1. **Automatic Endpoint Generation**: No need to write controller methods
2. **HATEOAS Support**: All responses include navigation links
3. **Built-in Pagination**: Automatic pagination and sorting support
4. **HAL+JSON Format**: Responses follow HAL (Hypertext Application Language) specification
5. **Repository Method Exposure**: Custom repository methods automatically become search endpoints
6. **Event Handlers**: Use `@RepositoryEventHandler` for business logic (timestamps, validation)

## Error Responses

Spring Data REST provides structured error responses:

```json
{
  "timestamp": "2025-09-21T10:30:00.000+00:00",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "errors": [
    {
      "codes": ["NotBlank.user.firstName"],
      "defaultMessage": "First name is required",
      "objectName": "user",
      "field": "firstName",
      "rejectedValue": "",
      "bindingFailure": false,
      "code": "NotBlank"
    }
  ]
}
```
